import { useEffect, useRef } from 'react';
import { Track } from 'livekit-client';
// import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { RnnoiseWorkletNode, loadRnnoise } from '@sapphi-red/web-noise-suppressor';
import { useNoiseSuppressionContext } from '../context/indexContext';


export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const audioContext = useRef(null);
  const rnnoiseNode = useRef(null);
  const sourceNode = useRef(null);
  const destinationNode = useRef(null);
  const originalTrack = useRef(null);
  const processedStream = useRef(null);
  const savedDeviceId = useRef(null);

  const getMicState = () => {
    const micPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);
    const currentTrackDeviceId = micPublication?.track?.mediaStreamTrack?.getSettings()?.deviceId;

    const isDeviceChanged = savedDeviceId.current && currentTrackDeviceId &&
                           savedDeviceId.current !== currentTrackDeviceId &&
                           savedDeviceId.current !== 'undefined' &&
                           currentTrackDeviceId !== 'undefined';

    return {
      micPublication,
      isEnabled: micPublication?.isEnabled || false,
      hasTrack: !!micPublication?.track,
      isMuted: micPublication?.isMuted || false,
      isPublished: micPublication?.track && !micPublication?.isMuted,
      hasNoiseProcessor: !!rnnoiseNode.current,
      currentTrackDeviceId,
      savedDeviceId: savedDeviceId.current,
      selectedDeviceId: deviceIdAudio,
      isDeviceChanged
    };
  };

  const applyNoiseSuppression = async () => {
    try {
      console.log('🔧 Starting Sapphi-Red noise suppression...');
      const { micPublication, currentTrackDeviceId } = getMicState();
      const localAudioTrack = micPublication.track;

      originalTrack.current = localAudioTrack.mediaStreamTrack;
      savedDeviceId.current = currentTrackDeviceId;

      // Create AudioContext
      console.log('🔧 Creating AudioContext...');
      audioContext.current = new AudioContext();

      // Load RNNoise WASM - try multiple sources
      console.log('🔧 Loading RNNoise WASM...');
      let rnnoiseWasmBinary;
      try {
        // Try jsdelivr first (more reliable for WASM files)
        rnnoiseWasmBinary = await loadRnnoise({
          url: 'https://cdn.jsdelivr.net/npm/@sapphi-red/web-noise-suppressor@0.3.5/dist/rnnoise.wasm'
        });
        console.log('✅ Loaded WASM from jsdelivr CDN');
      } catch (error) {
        console.log('⚠️ Failed to load WASM from jsdelivr, trying unpkg...', error);
        try {
          rnnoiseWasmBinary = await loadRnnoise({
            url: 'https://unpkg.com/@sapphi-red/web-noise-suppressor@0.3.5/dist/rnnoise.wasm'
          });
          console.log('✅ Loaded WASM from unpkg CDN');
        } catch (error2) {
          console.log('⚠️ Failed to load WASM from unpkg, trying local copy...', error2);
          // Try the local copy we placed in public
          rnnoiseWasmBinary = await loadRnnoise({
            url: '/rnnoise.wasm'
          });
          console.log('✅ Loaded WASM from local copy');
        }
      }

      // Add AudioWorklet module - try different approaches
      console.log('🔧 Adding AudioWorklet module...');
      try {
        // Try loading from unpkg CDN with specific version
        await audioContext.current.audioWorklet.addModule('https://unpkg.com/@sapphi-red/web-noise-suppressor@0.3.5/dist/rnnoise/workletProcessor.js');
        console.log('✅ Loaded worklet from unpkg CDN');
      } catch (error) {
        console.log('⚠️ Failed to load from unpkg, trying jsdelivr...', error);
        try {
          await audioContext.current.audioWorklet.addModule('https://cdn.jsdelivr.net/npm/@sapphi-red/web-noise-suppressor@0.3.5/dist/rnnoise/workletProcessor.js');
          console.log('✅ Loaded worklet from jsdelivr CDN');
        } catch (error2) {
          console.error('❌ Failed to load worklet from both CDNs:', error2);
          throw error2;
        }
      }

      // Create RNNoise worklet node
      console.log('🔧 Creating RNNoise worklet node...');
      rnnoiseNode.current = new RnnoiseWorkletNode(audioContext.current, {
        wasmBinary: rnnoiseWasmBinary,
        maxChannels: 1 // Mono audio for better performance
      });

      // Create source from original track
      console.log('🔧 Creating audio source...');
      const originalStream = new MediaStream([originalTrack.current]);
      sourceNode.current = audioContext.current.createMediaStreamSource(originalStream);

      // Create destination for processed audio
      console.log('🔧 Creating audio destination...');
      destinationNode.current = audioContext.current.createMediaStreamDestination();

      // Connect the audio graph: source -> rnnoise -> destination
      console.log('🔧 Connecting audio graph...');
      sourceNode.current.connect(rnnoiseNode.current);
      rnnoiseNode.current.connect(destinationNode.current);

      // Get the processed track
      const processedTrack = destinationNode.current.stream.getAudioTracks()[0];
      processedStream.current = destinationNode.current.stream;

      if (processedTrack) {
        console.log('✅ Sapphi-Red noise suppression applied successfully');
        await localAudioTrack.replaceTrack(processedTrack, true);
      } else {
        console.error('❌ Failed to get processed track from Sapphi-Red');
      }
    } catch (error) {
      console.error('❌ Error applying Sapphi-Red noise suppression:', error);
    }
  };

  const stopNoiseSuppression = async () => {
    try {
      console.log('🛑 Stopping Sapphi-Red noise suppression...');

      if (rnnoiseNode.current || audioContext.current) {
        const { micPublication } = getMicState();

        // Restore original track if available
        if (micPublication?.track && originalTrack.current) {
          console.log('🔄 Restoring original audio track...');
          await micPublication.track.replaceTrack(originalTrack.current, true);
        }

        // Disconnect and clean up audio nodes
        console.log('🧹 Cleaning up audio nodes...');
        if (sourceNode.current) {
          sourceNode.current.disconnect();
          sourceNode.current = null;
        }

        if (rnnoiseNode.current) {
          rnnoiseNode.current.disconnect();
          rnnoiseNode.current = null;
        }

        if (destinationNode.current) {
          destinationNode.current = null;
        }

        // Close AudioContext
        if (audioContext.current) {
          console.log('🔇 Closing AudioContext...');
          await audioContext.current.close();
          audioContext.current = null;
        }

        // Clean up references
        processedStream.current = null;
        originalTrack.current = null;
        savedDeviceId.current = null;

        console.log('✅ Sapphi-Red noise suppression stopped successfully');
      }
    } catch (error) {
      console.error('❌ Error stopping Sapphi-Red noise suppression:', error);
    }
  };

  const handleNoiseSuppressionLogic = async () => {
    const {
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      hasNoiseProcessor,
      currentTrackDeviceId,
      selectedDeviceId,
      isDeviceChanged
    } = getMicState();

    console.log('🎯 Sapphi-Red NS Logic - State:', {
      isNoiseSuppressionEnabled,
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      hasNoiseProcessor,
      isDeviceChanged
    });

    // NS Toggle is OFF
    if (!isNoiseSuppressionEnabled) {
      console.log('🔴 NS Toggle is OFF');
      if (hasNoiseProcessor) {
        console.log('🛑 Stopping existing processor...');
        await stopNoiseSuppression();
      }
      return;
    }

    // Device changed - handle with toggle disable/enable
    if (isDeviceChanged && hasNoiseProcessor && currentTrackDeviceId && selectedDeviceId) {
      console.log('🔄 Device changed detected');
      if (currentTrackDeviceId === selectedDeviceId) {
        console.log('🔄 Restarting NS for device change...');
        await stopNoiseSuppression();

        setIsNoiseSuppressionEnabled(false);

        setTimeout(() => {
          setIsNoiseSuppressionEnabled(true);
        }, 700);

        return;
      }
    }

    // NS Toggle is ON but no mic track
    if (!hasTrack) {
      console.log('⚠️ No mic track available');
      return;
    }

    // NS Toggle is ON, mic exists but muted
    if (isMuted) {
      console.log('🔇 Mic is muted');
      if (hasNoiseProcessor) {
        console.log('✅ NS already active, keeping it running while muted');
        return;
      } else {
        console.log('⏸️ Waiting for unmute to apply NS');
        return;
      }
    }

    // NS Toggle is ON, mic unmuted and published
    if (isPublished) {
      console.log('🎤 Mic is published and unmuted');
      if (hasNoiseProcessor) {
        console.log('✅ NS already active');
        return;
      } else {
        console.log('🚀 Applying Sapphi-Red NS in 1 second...');
        setTimeout(async () => {
          try {
            await applyNoiseSuppression();
          } catch (error) {
            console.error('❌ Delayed Sapphi-Red noise suppression error:', error);
          }
        }, 1000);

        return;
      }
    }

    // Mic disabled - no action needed
    if (!isEnabled) {
      console.log('🔇 Mic is disabled - no action needed');
    }
  };

  useEffect(() => {
    if (!room?.localParticipant) {
      console.log('⚠️ No room or local participant available');
      return;
    }
    console.log('🔄 useEffect triggered - running Sapphi-Red NS logic');
    handleNoiseSuppressionLogic();
  }, [
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isEnabled,
    deviceIdAudio,
    isNoiseSuppressionEnabled,
    isNoiseSuppressionEnabled && !rnnoiseNode.current ?
      room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isMuted : null
  ]);

  useEffect(() => {
    return () => {
      console.log('🧹 Component unmounting - cleaning up Sapphi-Red NS');
      stopNoiseSuppression();
    };
  }, []);

  return {
    isNoiseSuppressionActive: !!rnnoiseNode.current,
    hasOriginalTrack: !!originalTrack.current,
    stopNoiseSuppression
  };
};
