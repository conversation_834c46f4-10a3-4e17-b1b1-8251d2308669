import { useEffect, useRef } from 'react';
import { Track } from 'livekit-client';
// import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { NoiseGateWorkletNode } from '@sapphi-red/web-noise-suppressor';
import { useNoiseSuppressionContext } from '../context/indexContext';


export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const audioContext = useRef(null);
  const noiseGateNode = useRef(null);
  const sourceNode = useRef(null);
  const destinationNode = useRef(null);
  const originalTrack = useRef(null);
  const processedStream = useRef(null);
  const savedDeviceId = useRef(null);

  const getMicState = () => {
    const micPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);
    const currentTrackDeviceId = micPublication?.track?.mediaStreamTrack?.getSettings()?.deviceId;

    const isDeviceChanged = savedDeviceId.current && currentTrackDeviceId &&
                           savedDeviceId.current !== currentTrackDeviceId &&
                           savedDeviceId.current !== 'undefined' &&
                           currentTrackDeviceId !== 'undefined';

    return {
      micPublication,
      isEnabled: micPublication?.isEnabled || false,
      hasTrack: !!micPublication?.track,
      isMuted: micPublication?.isMuted || false,
      isPublished: micPublication?.track && !micPublication?.isMuted,
      hasNoiseProcessor: !!noiseGateNode.current,
      currentTrackDeviceId,
      savedDeviceId: savedDeviceId.current,
      selectedDeviceId: deviceIdAudio,
      isDeviceChanged
    };
  };

  const applyNoiseSuppression = async () => {
    try {
      console.log('🔧 Starting Sapphi-Red NoiseGate...');
      const { micPublication, currentTrackDeviceId } = getMicState();
      const localAudioTrack = micPublication.track;

      originalTrack.current = localAudioTrack.mediaStreamTrack;
      savedDeviceId.current = currentTrackDeviceId;

      // Create AudioContext
      console.log('🔧 Creating AudioContext...');
      audioContext.current = new AudioContext();

      // Ensure AudioContext is running
      if (audioContext.current.state === 'suspended') {
        console.log('🔧 Resuming suspended AudioContext...');
        await audioContext.current.resume();
      }
      console.log('🔧 AudioContext state:', audioContext.current.state);

      // Add NoiseGate AudioWorklet module
      console.log('🔧 Adding NoiseGate AudioWorklet module...');
      try {
        await audioContext.current.audioWorklet.addModule('https://unpkg.com/@sapphi-red/web-noise-suppressor@0.3.5/dist/noiseGate/workletProcessor.js');
        console.log('✅ Loaded NoiseGate worklet from unpkg CDN');
      } catch (error) {
        console.log('⚠️ Failed to load from unpkg, trying jsdelivr...', error);
        try {
          await audioContext.current.audioWorklet.addModule('https://cdn.jsdelivr.net/npm/@sapphi-red/web-noise-suppressor@0.3.5/dist/noiseGate/workletProcessor.js');
          console.log('✅ Loaded NoiseGate worklet from jsdelivr CDN');
        } catch (error2) {
          console.error('❌ Failed to load NoiseGate worklet from both CDNs:', error2);
          throw error2;
        }
      }

      // Create NoiseGate worklet node (no WASM needed!)
      console.log('🔧 Creating NoiseGate worklet node...');
      noiseGateNode.current = new NoiseGateWorkletNode(audioContext.current, {
        threshold: -50, // dB threshold - much more permissive (was -30)
        attack: 0.001,  // Attack time in seconds (faster)
        release: 0.05,  // Release time in seconds (faster)
        maxChannels: 1  // Mono audio
      });

      console.log('🔧 NoiseGate settings: threshold=-50dB, attack=1ms, release=50ms');

      // Create source from original track
      console.log('🔧 Creating audio source...');
      const originalStream = new MediaStream([originalTrack.current]);
      sourceNode.current = audioContext.current.createMediaStreamSource(originalStream);

      // Create destination for processed audio
      console.log('🔧 Creating audio destination...');
      destinationNode.current = audioContext.current.createMediaStreamDestination();

      // Add audio level monitoring for debugging
      const inputAnalyser = audioContext.current.createAnalyser();
      const outputAnalyser = audioContext.current.createAnalyser();
      inputAnalyser.fftSize = 256;
      outputAnalyser.fftSize = 256;
      const inputDataArray = new Uint8Array(inputAnalyser.frequencyBinCount);
      const outputDataArray = new Uint8Array(outputAnalyser.frequencyBinCount);

      // Connect the audio graph: source -> inputAnalyser -> noiseGate -> outputAnalyser -> destination
      console.log('🔧 Connecting audio graph with input/output monitoring...');
      sourceNode.current.connect(inputAnalyser);

      // TEMPORARY: Bypass NoiseGate to test if audio graph works
      console.log('⚠️ TESTING: Bypassing NoiseGate - connecting directly');
      inputAnalyser.connect(outputAnalyser);
      outputAnalyser.connect(destinationNode.current);

      // Original NoiseGate connection (commented out for testing)
      // inputAnalyser.connect(noiseGateNode.current);
      // noiseGateNode.current.connect(outputAnalyser);

      // Monitor audio levels for debugging
      const monitorAudio = () => {
        // Input levels (before NoiseGate)
        inputAnalyser.getByteFrequencyData(inputDataArray);
        const inputAverage = inputDataArray.reduce((a, b) => a + b) / inputDataArray.length;
        const inputDB = 20 * Math.log10(inputAverage / 255);

        // Output levels (after NoiseGate)
        outputAnalyser.getByteFrequencyData(outputDataArray);
        const outputAverage = outputDataArray.reduce((a, b) => a + b) / outputDataArray.length;
        const outputDB = 20 * Math.log10(outputAverage / 255);

        if (inputAverage > 0 || outputAverage > 0) {
          console.log('🎵 Input level:', inputAverage.toFixed(0), 'dB:', inputDB.toFixed(1),
                     '| Output level:', outputAverage.toFixed(0), 'dB:', outputDB.toFixed(1));
        }
      };

      // Log audio levels every 2 seconds for debugging
      const audioMonitor = setInterval(monitorAudio, 2000);

      // Store the monitor so we can clear it later
      noiseGateNode.current.audioMonitor = audioMonitor;

      // Get the processed track
      const processedTrack = destinationNode.current.stream.getAudioTracks()[0];
      processedStream.current = destinationNode.current.stream;

      if (processedTrack) {
        console.log('✅ Sapphi-Red NoiseGate applied successfully');
        console.log('🔧 Original track info:', {
          id: originalTrack.current.id,
          kind: originalTrack.current.kind,
          enabled: originalTrack.current.enabled,
          readyState: originalTrack.current.readyState,
          muted: originalTrack.current.muted
        });
        console.log('🔧 Processed track info:', {
          id: processedTrack.id,
          kind: processedTrack.kind,
          enabled: processedTrack.enabled,
          readyState: processedTrack.readyState,
          muted: processedTrack.muted
        });

        console.log('🔄 Replacing LiveKit track...');
        await localAudioTrack.replaceTrack(processedTrack, true);
        console.log('✅ LiveKit track replaced successfully');

        // Verify the replacement worked
        const currentTrack = localAudioTrack.mediaStreamTrack;
        console.log('🔍 Current LiveKit track after replacement:', {
          id: currentTrack.id,
          kind: currentTrack.kind,
          enabled: currentTrack.enabled,
          readyState: currentTrack.readyState,
          muted: currentTrack.muted
        });
      } else {
        console.error('❌ Failed to get processed track from NoiseGate');
      }
    } catch (error) {
      console.error('❌ Error applying Sapphi-Red NoiseGate:', error);
    }
  };

  const stopNoiseSuppression = async () => {
    try {
      console.log('🛑 Stopping Sapphi-Red NoiseGate...');

      if (noiseGateNode.current || audioContext.current) {
        const { micPublication } = getMicState();

        // Restore original track if available
        if (micPublication?.track && originalTrack.current) {
          console.log('🔄 Restoring original audio track...');
          await micPublication.track.replaceTrack(originalTrack.current, true);
        }

        // Disconnect and clean up audio nodes
        console.log('🧹 Cleaning up audio nodes...');
        if (sourceNode.current) {
          sourceNode.current.disconnect();
          sourceNode.current = null;
        }

        if (noiseGateNode.current) {
          // Clear audio monitor if it exists
          if (noiseGateNode.current.audioMonitor) {
            clearInterval(noiseGateNode.current.audioMonitor);
          }
          noiseGateNode.current.disconnect();
          noiseGateNode.current = null;
        }

        if (destinationNode.current) {
          destinationNode.current = null;
        }

        // Close AudioContext
        if (audioContext.current) {
          console.log('🔇 Closing AudioContext...');
          await audioContext.current.close();
          audioContext.current = null;
        }

        // Clean up references
        processedStream.current = null;
        originalTrack.current = null;
        savedDeviceId.current = null;

        console.log('✅ Sapphi-Red NoiseGate stopped successfully');
      }
    } catch (error) {
      console.error('❌ Error stopping Sapphi-Red NoiseGate:', error);
    }
  };

  const handleNoiseSuppressionLogic = async () => {
    const {
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      hasNoiseProcessor,
      currentTrackDeviceId,
      selectedDeviceId,
      isDeviceChanged
    } = getMicState();

    console.log('🎯 Sapphi-Red NoiseGate Logic - State:', {
      isNoiseSuppressionEnabled,
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      hasNoiseProcessor,
      isDeviceChanged
    });

    // NS Toggle is OFF
    if (!isNoiseSuppressionEnabled) {
      console.log('🔴 NS Toggle is OFF');
      if (hasNoiseProcessor) {
        console.log('🛑 Stopping existing processor...');
        await stopNoiseSuppression();
      }
      return;
    }

    // Device changed - handle with toggle disable/enable
    if (isDeviceChanged && hasNoiseProcessor && currentTrackDeviceId && selectedDeviceId) {
      console.log('🔄 Device changed detected');
      if (currentTrackDeviceId === selectedDeviceId) {
        console.log('🔄 Restarting NS for device change...');
        await stopNoiseSuppression();

        setIsNoiseSuppressionEnabled(false);

        setTimeout(() => {
          setIsNoiseSuppressionEnabled(true);
        }, 700);

        return;
      }
    }

    // NS Toggle is ON but no mic track
    if (!hasTrack) {
      console.log('⚠️ No mic track available');
      return;
    }

    // NS Toggle is ON, mic exists but muted
    if (isMuted) {
      console.log('🔇 Mic is muted');
      if (hasNoiseProcessor) {
        console.log('✅ NS already active, keeping it running while muted');
        return;
      } else {
        console.log('⏸️ Waiting for unmute to apply NS');
        return;
      }
    }

    // NS Toggle is ON, mic unmuted and published
    if (isPublished) {
      console.log('🎤 Mic is published and unmuted');
      if (hasNoiseProcessor) {
        console.log('✅ NS already active');
        return;
      } else {
        console.log('🚀 Applying Sapphi-Red NoiseGate in 1 second...');
        setTimeout(async () => {
          try {
            await applyNoiseSuppression();
          } catch (error) {
            console.error('❌ Delayed Sapphi-Red NoiseGate error:', error);
          }
        }, 1000);

        return;
      }
    }

    // Mic disabled - no action needed
    if (!isEnabled) {
      console.log('🔇 Mic is disabled - no action needed');
    }
  };

  useEffect(() => {
    if (!room?.localParticipant) {
      console.log('⚠️ No room or local participant available');
      return;
    }
    console.log('🔄 useEffect triggered - running Sapphi-Red NoiseGate logic');
    handleNoiseSuppressionLogic();
  }, [
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isEnabled,
    deviceIdAudio,
    isNoiseSuppressionEnabled,
    isNoiseSuppressionEnabled && !noiseGateNode.current ?
      room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isMuted : null
  ]);

  useEffect(() => {
    return () => {
      console.log('🧹 Component unmounting - cleaning up Sapphi-Red NoiseGate');
      stopNoiseSuppression();
    };
  }, []);

  return {
    isNoiseSuppressionActive: !!noiseGateNode.current,
    hasOriginalTrack: !!originalTrack.current,
    stopNoiseSuppression
  };
};
